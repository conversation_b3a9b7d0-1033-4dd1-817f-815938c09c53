"""
Test the gradient_analysis module as a standalone module.

This test verifies that the gradient_analysis module can be imported
and used independently of the pickpoint_segmenter module.
"""

import numpy as np
import pytest

from gradient_analysis import compute_inward_gradient_score


class TestGradientAnalysisModule:
    """Test the gradient_analysis module as a standalone component."""

    def test_module_import(self):
        """Test that the module can be imported successfully."""
        # This test passes if the import at the top of the file succeeds
        assert compute_inward_gradient_score is not None

    def test_standalone_gradient_computation(self):
        """Test that the module works independently."""
        # Create simple test data
        mask = np.zeros((50, 50), dtype=bool)
        mask[20:30, 20:30] = True

        depth_image = np.zeros((50, 50, 3), dtype=np.uint8)
        depth_image[20:30, 20:30] = 255

        # Test the main function
        score = compute_inward_gradient_score(mask, depth_image)

        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0.8  # Should be high for this simple case

    def test_module_with_different_input_formats(self):
        """Test that the module handles different input formats."""
        mask = np.zeros((30, 30), dtype=bool)
        mask[10:20, 10:20] = True

        # Test with BGR image
        depth_bgr = np.zeros((30, 30, 3), dtype=np.uint8)
        depth_bgr[10:20, 10:20] = [255, 255, 255]

        score_bgr = compute_inward_gradient_score(mask, depth_bgr)
        assert 0.0 <= score_bgr <= 1.0

        # Test with grayscale image
        depth_gray = np.zeros((30, 30), dtype=np.uint8)
        depth_gray[10:20, 10:20] = 255

        score_gray = compute_inward_gradient_score(mask, depth_gray)
        assert 0.0 <= score_gray <= 1.0

        # Scores should be similar (allowing for small differences due to color conversion)
        assert abs(score_bgr - score_gray) < 0.1

    def test_module_documentation(self):
        """Test that the module functions have proper documentation."""
        assert compute_inward_gradient_score.__doc__ is not None
        assert len(compute_inward_gradient_score.__doc__) > 100  # Substantial documentation

        # Check that key terms are mentioned in documentation
        main_doc = compute_inward_gradient_score.__doc__.lower()
        assert "gradient" in main_doc
        assert "inward" in main_doc
        assert "contour" in main_doc
        assert "mask" in main_doc


if __name__ == "__main__":
    pytest.main([__file__])
