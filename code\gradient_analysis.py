"""
Gradient analysis module for pickpoint segmentation.

This module provides functionality to analyze depth gradients along object contours
to determine the quality of segmentation masks. It implements a gradient-walking
algorithm that checks whether gradients point inward along the contour, which is
a strong indicator of true object boundaries.
"""

import cv2 as cv
import numpy as np


def compute_inward_gradient_score(mask: np.ndarray, depth_image: np.ndarray, ksize=3) -> float:
    """
    Compute the fraction of the mask contour where depth gradients point inward.

    This function analyzes the depth gradients along the contour of a segmentation mask
    to determine how well the mask represents a true object boundary. It uses a
    gradient-walking approach: for each contour point, it takes a small step in the
    gradient direction and checks if this leads into or out of the mask.

    Args:
        mask: Boolean mask of the segmented object (2D numpy array)
        depth_image: Depth image in grayscale format (2D)
        ksize: Kernel size for Sobel operator (default=3)

    Returns:
        Float between 0 and 1 representing the fraction of contour points
        where the gradient points inward (indicating good object boundaries).

        - 1.0: Perfect score - all gradients point inward (ideal object boundary)
        - 0.5: Moderate score - half of gradients point inward
        - 0.0: Poor score - no gradients point inward (poor boundary quality)

    Algorithm:
        1. Convert depth image to grayscale if needed
        2. Compute gradients using Sobel operators
        3. Extract the largest contour from the mask
        4. For each contour point:
           - Skip boundary points and points with small gradients
           - Normalize the gradient vector
           - Take a small step in the gradient direction
           - Check if the step leads into the mask (inward gradient)
        5. Return the fraction of points with inward gradients

    Example:
        >>> mask = np.zeros((100, 100), dtype=bool)
        >>> mask[25:75, 25:75] = True  # Square mask
        >>> depth_image = np.zeros((100, 100), dtype=np.uint8)
        >>> depth_image[25:75, 25:75] = 255  # White square on black background
        >>> score = compute_inward_gradient_score(mask, depth_image)
        >>> print(f"Gradient score: {score:.3f}")  # Should be close to 1.0
    """

    # Compute gradients using Sobel operators
    grad_x = cv.Sobel(depth_image, cv.CV_64F, 1, 0, ksize=ksize)
    grad_y = cv.Sobel(depth_image, cv.CV_64F, 0, 1, ksize=ksize)

    # Find contours of the mask
    mask_uint8 = (mask * 255).astype(np.uint8)
    contours, _ = cv.findContours(mask_uint8, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_NONE)

    if not contours:
        return 0.0

    # Use the largest contour (should be the main object boundary)
    main_contour = max(contours, key=cv.contourArea)

    if len(main_contour) < 10:  # Too few points for reliable analysis
        return 0.0

    inward_count = 0
    total_count = 0

    for point in main_contour:
        x, y = point[0]

        # Skip points at image boundaries
        if x <= 1 or y <= 1 or x >= depth_image.shape[1] - 2 or y >= depth_image.shape[0] - 2:
            continue

        # Get gradient at this point
        gx = grad_x[y, x]
        gy = grad_y[y, x]

        # Skip if gradient is too small (no significant depth change)
        gradient_magnitude = gx**2 + gy**2
        if gradient_magnitude < 1e-6:
            continue

        # Check if gradient points inward by walking along the gradient direction
        # Normalize gradient vector
        gx_norm = gx / gradient_magnitude
        gy_norm = gy / gradient_magnitude

        # Take a small step in the gradient direction
        step_size = 2.0  # pixels
        step_x = x + step_size * gx_norm
        step_y = y + step_size * gy_norm

        # Check if the step lands inside or outside the mask
        step_x_int = int(round(step_x))
        step_y_int = int(round(step_y))

        # Ensure step point is within image bounds
        if 0 <= step_x_int < mask.shape[1] and 0 <= step_y_int < mask.shape[0]:
            # If stepping in gradient direction leads us INTO the mask,
            # then the gradient points inward
            if mask[step_y_int, step_x_int]:
                inward_count += 1

        total_count += 1

    if total_count == 0:
        return 0.0

    return inward_count / total_count
